import { useState, useRef } from 'react'
import SignatureCanvas from 'react-signature-canvas'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import './ConsentForm.css'

const ConsentForm = () => {
  const [participantName, setParticipantName] = useState('')
  const [date, setDate] = useState(new Date().toISOString().split('T')[0])
  const [isSigned, setIsSigned] = useState(false)
  const sigCanvas = useRef({})
  const formRef = useRef()

  const clearSignature = () => {
    sigCanvas.current.clear()
    setIsSigned(false)
  }

  const handleSignatureEnd = () => {
    setIsSigned(!sigCanvas.current.isEmpty())
  }



  const createSignatureImage = () => {
    // Create a new canvas with white background to ensure visibility
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    // Set canvas size to match signature canvas
    canvas.width = 500
    canvas.height = 200

    // Fill with white background
    ctx.fillStyle = '#ffffff'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Get the original signature canvas
    const originalCanvas = sigCanvas.current.getCanvas()

    // Verify the original canvas has actual signature data
    const originalImageData = originalCanvas.getContext('2d').getImageData(0, 0, originalCanvas.width, originalCanvas.height)
    const pixels = originalImageData.data
    let hasNonWhitePixels = false

    // Check for non-white pixels (signature content)
    for (let i = 0; i < pixels.length; i += 4) {
      const r = pixels[i]
      const g = pixels[i + 1]
      const b = pixels[i + 2]
      const a = pixels[i + 3]

      // If we find a pixel that's not white or transparent, we have signature content
      if (a > 0 && (r < 255 || g < 255 || b < 255)) {
        hasNonWhitePixels = true
        break
      }
    }

    if (!hasNonWhitePixels) {
      console.error('❌ No signature content detected in canvas')
      throw new Error('Signature canvas appears to be empty')
    }

    // Draw the signature on top of the white background
    ctx.drawImage(originalCanvas, 0, 0)

    // Get the final image data
    const signatureData = canvas.toDataURL('image/png')

    console.log('🖊️ Signature data length:', signatureData.length)
    console.log('🖊️ Signature data preview:', signatureData.substring(0, 100))

    // Additional validation: check if signature data is substantial
    if (signatureData.length < 2000) {
      console.error('❌ Signature data appears too small')
      throw new Error('Generated signature image is too small to be valid')
    }

    console.log('✅ Signature data validation passed!')
    return Promise.resolve(signatureData)
  }

  const generatePDF = async () => {
    if (!participantName.trim()) {
      alert('Please enter participant name')
      return
    }

    if (!isSigned) {
      alert('Please provide your signature')
      return
    }

    // Additional signature validation - check if canvas is truly empty
    if (sigCanvas.current.isEmpty()) {
      alert('Please provide your signature before generating PDF')
      setIsSigned(false)
      return
    }

    try {
      // Create clean signature image with validation
      let signatureImageData
      try {
        signatureImageData = await createSignatureImage()
      } catch (signatureError) {
        console.error('❌ Signature validation failed:', signatureError.message)
        alert(`Signature validation failed: ${signatureError.message}. Please ensure you have provided a clear signature.`)
        return
      }

      // Hide form controls and signature canvas during capture
      const controls = document.querySelectorAll('.form-controls')
      const signatureCanvas = document.querySelector('.signature-canvas')

      controls.forEach(control => control.style.display = 'none')
      signatureCanvas.style.visibility = 'hidden' // Hide but keep space

      // Capture the form
      const canvas = await html2canvas(formRef.current, {
        scale: 1.5,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false
      })

      // Show controls and signature canvas again
      controls.forEach(control => control.style.display = 'block')
      signatureCanvas.style.visibility = 'visible'

      // Create PDF
      const pdf = new jsPDF('p', 'mm', 'a4')

      // Add the form image to PDF
      const formImgData = canvas.toDataURL('image/png')
      const pdfWidth = pdf.internal.pageSize.getWidth()
      const pdfHeight = (canvas.height * pdfWidth) / canvas.width

      // Add form to PDF (handle multiple pages if needed)
      const pageHeight = pdf.internal.pageSize.getHeight()

      if (pdfHeight <= pageHeight) {
        pdf.addImage(formImgData, 'PNG', 0, 0, pdfWidth, pdfHeight)
      } else {
        let heightLeft = pdfHeight
        let position = 0
        pdf.addImage(formImgData, 'PNG', 0, position, pdfWidth, pdfHeight)
        heightLeft -= pageHeight
        while (heightLeft >= 0) {
          position = heightLeft - pdfHeight
          pdf.addPage()
          pdf.addImage(formImgData, 'PNG', 0, position, pdfWidth, pdfHeight)
          heightLeft -= pageHeight
        }
      }

      // Simplified signature positioning - place it in a fixed location in the signature area
      // This is more reliable than complex positioning calculations

      // Position signature in the lower right area of the PDF where the signature section is
      const signatureWidth = 80  // mm - reasonable size for signature
      const signatureHeight = 30 // mm - reasonable height for signature

      // Place signature towards the bottom of the PDF, in the signature area
      const finalX = pdfWidth - signatureWidth - 20  // 20mm margin from right
      const finalY = pdfHeight - signatureHeight - 40 // 40mm margin from bottom

      console.log('Simplified signature positioning:', {
        pdfWidth,
        pdfHeight,
        signatureWidth,
        signatureHeight,
        finalX,
        finalY
      })

      // Verify signature image data before adding to PDF
      console.log('📄 About to add signature to PDF:', {
        imageDataLength: signatureImageData.length,
        imageDataPreview: signatureImageData.substring(0, 50),
        position: { x: finalX, y: finalY },
        size: { width: signatureWidth, height: signatureHeight }
      })

      // Add the signature image to the PDF
      console.log('📄 Adding signature to PDF with dimensions:', {
        signatureWidth,
        signatureHeight,
        finalX,
        finalY,
        pdfWidth,
        pdfHeight
      })

      // Try multiple approaches to ensure signature is visible
      let signatureAdded = false

      // Approach 1: Use the processed signature image
      try {
        pdf.addImage(signatureImageData, 'PNG', finalX, finalY, signatureWidth, signatureHeight)
        console.log('✅ Signature successfully added to PDF at position:', finalX, finalY)
        signatureAdded = true
      } catch (signatureError) {
        console.warn('⚠️ Primary signature placement failed:', signatureError)
      }

      // Approach 2: If first approach failed, try direct canvas data
      if (!signatureAdded) {
        try {
          const directSignatureData = sigCanvas.current.toDataURL('image/png')
          console.log('🔄 Trying direct canvas data, length:', directSignatureData.length)
          pdf.addImage(directSignatureData, 'PNG', finalX, finalY, signatureWidth, signatureHeight)
          console.log('✅ Signature added using direct canvas data')
          signatureAdded = true
        } catch (fallbackError) {
          console.warn('⚠️ Direct canvas approach failed:', fallbackError)
        }
      }

      // Approach 3: If both failed, add at a very visible fixed position
      if (!signatureAdded) {
        try {
          const directSignatureData = sigCanvas.current.toDataURL('image/png')
          // Add signature prominently at the bottom of the first page
          pdf.addImage(directSignatureData, 'PNG', 20, pdfHeight - 80, 120, 40)
          console.log('✅ Signature added at prominent fallback position')
          signatureAdded = true
        } catch (finalError) {
          console.error('❌ All signature placement attempts failed:', finalError)
        }
      }

      // Also add a text label to make signature area more obvious
      if (signatureAdded) {
        pdf.setFontSize(8)
        pdf.text('Digital Signature', finalX, finalY + signatureHeight + 5)
      }

      // Save the PDF
      const fileName = `GENESIS_Consent_${participantName.replace(/\s+/g, '_')}_${date}.pdf`
      pdf.save(fileName)
      alert('PDF generated successfully with signature!')

    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Error generating PDF. Please try again.')
    }
  }

  const generateImage = async () => {
    if (!participantName.trim()) {
      alert('Please enter participant name')
      return
    }

    if (!isSigned) {
      alert('Please provide your signature')
      return
    }

    // Additional signature validation - check if canvas is truly empty
    if (sigCanvas.current.isEmpty()) {
      alert('Please provide your signature before generating image')
      setIsSigned(false)
      return
    }

    try {
      // Create clean signature image with validation
      let signatureImageData
      try {
        signatureImageData = await createSignatureImage()
      } catch (signatureError) {
        console.error('❌ Signature validation failed:', signatureError.message)
        alert(`Signature validation failed: ${signatureError.message}. Please ensure you have provided a clear signature.`)
        return
      }

      console.log('🖼️ Generating image version for debugging...')

      // Hide form controls during capture
      const controls = document.querySelectorAll('.form-controls')
      controls.forEach(control => control.style.display = 'none')

      // Capture the form as image
      const canvas = await html2canvas(formRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: true
      })

      // Show controls again
      controls.forEach(control => control.style.display = 'block')

      // Create a new canvas to composite the form and signature
      const compositeCanvas = document.createElement('canvas')
      const ctx = compositeCanvas.getContext('2d')

      // Set canvas size to match the captured form
      compositeCanvas.width = canvas.width
      compositeCanvas.height = canvas.height

      // Draw the form
      ctx.drawImage(canvas, 0, 0)

      // Find signature position on the captured canvas
      const signatureContainer = document.querySelector('.signature-canvas-container')
      const formRect = formRef.current.getBoundingClientRect()
      const sigRect = signatureContainer.getBoundingClientRect()

      // Calculate relative position and scale for the high-res canvas
      const scale = 2
      const relativeTop = (sigRect.top - formRect.top) * scale
      const relativeLeft = (sigRect.left - formRect.left) * scale
      const signatureWidth = 500 * scale
      const signatureHeight = 200 * scale

      console.log('🖼️ Image signature positioning:', {
        relativeTop,
        relativeLeft,
        signatureWidth,
        signatureHeight,
        canvasWidth: compositeCanvas.width,
        canvasHeight: compositeCanvas.height
      })

      // Create signature image and draw it on the composite
      const signatureImg = new Image()
      signatureImg.onload = () => {
        // Draw signature on the composite canvas
        ctx.drawImage(signatureImg, relativeLeft, relativeTop, signatureWidth, signatureHeight)

        // Convert to downloadable image
        const finalImageData = compositeCanvas.toDataURL('image/png')

        // Create download link
        const link = document.createElement('a')
        link.download = `GENESIS_Consent_${participantName.replace(/\s+/g, '_')}_${date}.png`
        link.href = finalImageData
        link.click()

        console.log('✅ Image generated successfully with signature!')
        alert('Image generated successfully with signature!')
      }

      signatureImg.onerror = () => {
        console.error('❌ Failed to load signature image for composite')
        // Fallback: just download the form without signature overlay
        const link = document.createElement('a')
        link.download = `GENESIS_Consent_${participantName.replace(/\s+/g, '_')}_${date}_no_sig.png`
        link.href = canvas.toDataURL('image/png')
        link.click()
        alert('Image generated (signature overlay failed)')
      }

      signatureImg.src = signatureImageData

    } catch (error) {
      console.error('Error generating image:', error)
      alert('Error generating image. Please try again.')
    }
  }

  return (
    <div className="consent-container">
      <div ref={formRef} className="consent-form">
        <header className="form-header">
          <h1>GENESIS Program Consent and Acknowledgment</h1>
          <p className="subtitle">
            Thank you for agreeing to enroll in <strong>GENESIS</strong>, a program of{' '}
            <strong>Roseman Medical Group</strong> and{' '}
            <strong>Roseman University College of Medicine</strong>. This form outlines the program details and its limitations.
          </p>
        </header>

        <section className="form-section">
          <h2>Authorization</h2>
          <p>
            I authorize <strong>Roseman Medical Group</strong>, <strong>GENESIS</strong>, and{' '}
            <strong>Roseman University of Health Sciences</strong> to assist me and members of my household 
            in addressing urgent social needs by helping us access social services.
          </p>
        </section>

        <section className="form-section">
          <h2>Understanding of Services</h2>
          <ul>
            <li>I understand that <strong>social needs</strong> include help with food, transportation, health insurance, or housing.</li>
            <li>I understand that <strong>GENESIS</strong> and <strong>Roseman Medical Group</strong> do not directly provide these services, but will help connect me with organizations that can.</li>
            <li>I understand that <strong>GENESIS</strong> operates on behalf of <strong>Roseman Medical Group</strong> when assisting with social needs.</li>
            <li>I understand that assistance may be provided:
              <ul>
                <li>At my home</li>
                <li>At the Roseman Medical Group office</li>
                <li>At the location of a third-party service provider</li>
              </ul>
            </li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Medical Services</h2>
          <ul>
            <li>I understand that <strong>GENESIS</strong> may also help me find <strong>medical services</strong>, which may be provided by Roseman Medical Group or another local healthcare provider.</li>
            <li>I understand that any third-party services will have their own <strong>independent rules and fees</strong>.</li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Home Visits</h2>
          <ul>
            <li>I understand that <strong>home visits</strong> cannot begin until I have successfully completed a <strong>home environment assessment</strong>, which includes disclosing any <strong>past criminal history</strong> by household members.</li>
            <li>I understand that past criminal history <strong>does not automatically exclude</strong> me from participating in GENESIS.</li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Legal Reporting Requirements</h2>
          <ul>
            <li>In accordance with <strong>Nevada Revised Statute (NRS) Chapter 432B</strong>, any person with reasonable cause to believe that child abuse is occurring <strong>must report</strong> to Child Protective Services (CPS) or law enforcement.<br />
            👉 GENESIS staff are <strong>mandated reporters</strong> of suspected child abuse.</li>
            <li>Under <strong>NRS 200.5092</strong>, any professional who suspects abuse or neglect of an <strong>elderly person</strong> (age 60+) or vulnerable adult is <strong>legally required to report it</strong>.<br />
            👉 GENESIS staff are <strong>mandated reporters</strong> of suspected elder abuse.</li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Limitations and Privacy</h2>
          <ul>
            <li>I understand that the success of services depends on <strong>my own participation and effort</strong>.</li>
            <li>I acknowledge that there are <strong>no guarantees</strong> that GENESIS will be successful in securing assistance.</li>
            <li>I understand that <strong>information collected</strong> by GENESIS may be used for <strong>program evaluation</strong>.
              <ul>
                <li>No report or data summary will <strong>specifically identify me or my household</strong>.</li>
              </ul>
            </li>
          </ul>
        </section>

        <section className="form-section">
          <h2>Contact and Complaints</h2>
          <div className="contact-box">
            <p><em>Service excellence is our top priority. If you have a complaint about the service you've received, we take it seriously and are committed to resolving it.</em></p>
            <p>To file a formal complaint, please contact:<br />
            📞 <strong>************</strong></p>
          </div>
        </section>

        <section className="signature-section">
          <div className="participant-info">
            <div className="input-group">
              <label htmlFor="participantName">Participant Name:</label>
              <input
                type="text"
                id="participantName"
                value={participantName}
                onChange={(e) => setParticipantName(e.target.value)}
                placeholder="Enter full name"
                required
              />
            </div>
            <div className="input-group">
              <label htmlFor="date">Date:</label>
              <input
                type="date"
                id="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                required
              />
            </div>
          </div>

          <div className="signature-area">
            <label>Participant Signature:</label>
            <div className="signature-canvas-container">
              <SignatureCanvas
                ref={sigCanvas}
                penColor="#000000"
                backgroundColor="#ffffff"
                canvasProps={{
                  width: 500,
                  height: 200,
                  className: 'signature-canvas',
                  style: { backgroundColor: '#ffffff' }
                }}
                onEnd={handleSignatureEnd}
                clearOnResize={false}
              />
            </div>
            <div className="signature-line">
              <span>Signature</span>
            </div>
          </div>
        </section>
      </div>

      <div className="form-controls">
        <button onClick={clearSignature} className="btn btn-secondary">
          Clear Signature
        </button>
        <button onClick={generatePDF} className="btn btn-primary">
          Generate PDF
        </button>
        <button onClick={generateImage} className="btn btn-secondary">
          Generate Image
        </button>
      </div>
    </div>
  )
}

export default ConsentForm
